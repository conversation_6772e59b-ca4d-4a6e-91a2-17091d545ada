{"version": 3, "file": "WaterPass.js", "sources": ["../../src/postprocessing/WaterPass.ts"], "sourcesContent": ["import { Web<PERSON><PERSON>ender<PERSON>, WebGLRenderTarget, ShaderMaterial, Vector2, IUniform, Texture } from 'three'\nimport { Pass, FullScreenQuad } from '../postprocessing/Pass'\n\n/**\n * Simple underwater shader\n * \n \n parameters:\n tex: texture\n time: this should increase with time passing\n factor: to what degree will the shader distort the screen \n\n explaination:\n the shader is quite simple\n it chooses a center and start from there make pixels around it to \"swell\" then \"shrink\" then \"swell\"...\n this is of course nothing really similar to underwater scene\n but you can combine several this shaders together to create the effect you need...\n And yes, this shader could be used for something other than underwater effect, for example, magnifier effect :)\n\n * <AUTHOR> Wang\n */\n\nclass WaterPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n  public factor: number\n  public time: number\n  public uniforms: {\n    tex: IUniform<Texture>\n    time: IUniform<number>\n    factor: IUniform<number>\n    resolution: IUniform<Vector2>\n  }\n\n  constructor() {\n    super()\n    this.uniforms = {\n      tex: { value: null! },\n      time: { value: 0.0 },\n      factor: { value: 0.0 },\n      resolution: { value: new Vector2(64, 64) },\n    }\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: `\n      varying vec2 vUv;\n      void main(){  \n        vUv = uv; \n        vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);\n        gl_Position = projectionMatrix * modelViewPosition;\n      }`,\n      fragmentShader: `\n      uniform float time;\n      uniform float factor;\n      uniform vec2 resolution;\n      uniform sampler2D tex;\n      varying vec2 vUv;\n      void main() {  \n        vec2 uv1 = vUv;\n        vec2 uv = gl_FragCoord.xy/resolution.xy;\n        float frequency = 6.0 * factor;\n        float amplitude = 0.015 * factor;\n        float x = uv1.y * frequency + time * .7; \n        float y = uv1.x * frequency + time * .3;\n        uv1.x += cos(x+y) * amplitude * cos(y);\n        uv1.y += sin(x-y) * amplitude * cos(y);\n        vec4 rgba = texture2D(tex, uv1);\n        gl_FragColor = rgba;\n      }`,\n    })\n    this.fsQuad = new FullScreenQuad(this.material)\n    this.factor = 0\n    this.time = 0\n  }\n\n  public render(renderer: WebGLRenderer, writeBuffer: WebGLRenderTarget, readBuffer: WebGLRenderTarget): void {\n    this.uniforms['tex'].value = readBuffer.texture\n    this.uniforms['time'].value = this.time\n    this.uniforms['factor'].value = this.factor\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n}\n\nexport { WaterPass }\n"], "names": [], "mappings": ";;;;;;;;AAsBA,MAAM,kBAAkB,KAAK;AAAA,EAY3B,cAAc;AACN;AAZD;AACA;AACA;AACA;AACA;AASL,SAAK,WAAW;AAAA,MACd,KAAK,EAAE,OAAO,KAAM;AAAA,MACpB,MAAM,EAAE,OAAO,EAAI;AAAA,MACnB,QAAQ,EAAE,OAAO,EAAI;AAAA,MACrB,YAAY,EAAE,OAAO,IAAI,QAAQ,IAAI,EAAE,EAAE;AAAA,IAAA;AAEtC,SAAA,WAAW,IAAI,eAAe;AAAA,MACjC,UAAU,KAAK;AAAA,MACf,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAA,CAkBjB;AACD,SAAK,SAAS,IAAI,eAAe,KAAK,QAAQ;AAC9C,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAAA,EAEO,OAAO,UAAyB,aAAgC,YAAqC;AAC1G,SAAK,SAAS,KAAK,EAAE,QAAQ,WAAW;AACxC,SAAK,SAAS,MAAM,EAAE,QAAQ,KAAK;AACnC,SAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK;AACrC,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAAA,OACtB;AACL,eAAS,gBAAgB,WAAW;AACpC,UAAI,KAAK;AAAO,iBAAS,MAAM;AAC1B,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AACF;"}