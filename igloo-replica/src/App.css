* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(180deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* UI Overlay */
.ui-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.ui-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.main-title {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #00d4ff, #ffffff, #00d4ff);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
}

@keyframes gradient-shift {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

.subtitle {
  font-size: clamp(1rem, 3vw, 1.5rem);
  font-weight: 300;
  max-width: 600px;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 3rem;
}



/* Glitch Effect */
.glitch {
  position: relative;
  animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #ff0040;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #00d4ff;
  z-index: -2;
}

@keyframes glitch {

  0%,
  100% {
    transform: translate(0);
  }

  20% {
    transform: translate(-2px, 2px);
  }

  40% {
    transform: translate(-2px, -2px);
  }

  60% {
    transform: translate(2px, 2px);
  }

  80% {
    transform: translate(2px, -2px);
  }
}

@keyframes glitch-1 {

  0%,
  100% {
    transform: translate(0);
  }

  20% {
    transform: translate(2px, -2px);
  }

  40% {
    transform: translate(-2px, 2px);
  }

  60% {
    transform: translate(-2px, -2px);
  }

  80% {
    transform: translate(2px, 2px);
  }
}

@keyframes glitch-2 {

  0%,
  100% {
    transform: translate(0);
  }

  20% {
    transform: translate(-2px, -2px);
  }

  40% {
    transform: translate(2px, 2px);
  }

  60% {
    transform: translate(2px, -2px);
  }

  80% {
    transform: translate(-2px, 2px);
  }
}

/* Social Links */
.social-links {
  display: flex;
  gap: 30px;
  margin-top: 2rem;
  pointer-events: auto;
}

.social-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.2);
}

.social-link:hover {
  color: #00d4ff;
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 212, 255, 0.2);
}

/* Cursor Glow Effect */
.cursor-glow {
  position: fixed;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: 1;
}

/* Enhanced Navigation */
.navigation {
  position: fixed;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  pointer-events: auto;
  padding: 15px 25px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.nav-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.2);
  opacity: 0;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: #00d4ff;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.nav-dot.active::before {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.5);
}

.nav-dot:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.2);
}

.nav-dot:hover::before {
  opacity: 0.5;
  transform: translate(-50%, -50%) scale(1.2);
}