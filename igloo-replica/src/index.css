@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0a0a0a;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(180deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

#root {
  width: 100%;
  height: 100%;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

a {
  font-weight: 500;
  color: #00d4ff;
  text-decoration: inherit;
  transition: color 0.3s ease;
}

a:hover {
  color: #ffffff;
}

button {
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

button:hover {
  border-color: #00d4ff;
  background-color: rgba(0, 212, 255, 0.1);
  transform: translateY(-2px);
}

button:focus,
button:focus-visible {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
}