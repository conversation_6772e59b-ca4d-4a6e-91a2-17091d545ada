import React, { useMemo, useRef } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

const IceMaterial = ({ color = '#e6f3ff', ...props }) => {
  const materialRef = useRef()

  const vertexShader = `
    varying vec3 vNormal;
    varying vec3 vPosition;
    varying vec2 vUv;
    
    void main() {
      vNormal = normalize(normalMatrix * normal);
      vPosition = position;
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `

  const fragmentShader = `
    uniform float time;
    uniform vec3 color;
    uniform float opacity;
    
    varying vec3 vNormal;
    varying vec3 vPosition;
    varying vec2 vUv;
    
    // Noise function
    float noise(vec3 p) {
      return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
    }
    
    void main() {
      vec3 normal = normalize(vNormal);
      
      // Create ice crystal effect with noise
      float noiseValue = noise(vPosition * 10.0 + time * 0.5);
      float fresnel = pow(1.0 - dot(normal, vec3(0.0, 0.0, 1.0)), 2.0);
      
      // Ice color with internal reflections
      vec3 iceColor = mix(color, vec3(0.8, 0.9, 1.0), fresnel);
      iceColor = mix(iceColor, vec3(0.0, 0.8, 1.0), noiseValue * 0.3);
      
      // Add sparkle effect
      float sparkle = step(0.98, noiseValue);
      iceColor += sparkle * vec3(1.0, 1.0, 1.0) * 0.5;
      
      // Calculate alpha with fresnel effect
      float alpha = opacity * (0.3 + fresnel * 0.7);
      
      gl_FragColor = vec4(iceColor, alpha);
    }
  `

  const uniforms = useMemo(() => ({
    time: { value: 0 },
    color: { value: new THREE.Color(color) },
    opacity: { value: 0.7 }
  }), [color])

  useFrame((state) => {
    if (materialRef.current) {
      materialRef.current.uniforms.time.value = state.clock.elapsedTime
    }
  })

  return (
    <shaderMaterial
      ref={materialRef}
      vertexShader={vertexShader}
      fragmentShader={fragmentShader}
      uniforms={uniforms}
      transparent
      side={THREE.DoubleSide}
      {...props}
    />
  )
}

export default IceMaterial
