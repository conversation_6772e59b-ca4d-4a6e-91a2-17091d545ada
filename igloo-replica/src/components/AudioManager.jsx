import React, { useEffect, useRef, useState } from 'react'

const AudioManager = ({ currentSection, isLoaded }) => {
  const audioContextRef = useRef(null)
  const oscillatorRef = useRef(null)
  const gainNodeRef = useRef(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)

  // Create Web Audio API context for procedural audio
  useEffect(() => {
    if (typeof window !== 'undefined' && window.AudioContext) {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
      gainNodeRef.current = audioContextRef.current.createGain()
      gainNodeRef.current.connect(audioContextRef.current.destination)
      gainNodeRef.current.gain.value = 0.1
    }

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [])

  // Create ambient winter sounds
  const createAmbientSound = () => {
    if (!audioContextRef.current || isMuted) return

    const context = audioContextRef.current
    
    // Wind sound using filtered noise
    const bufferSize = context.sampleRate * 2
    const buffer = context.createBuffer(1, bufferSize, context.sampleRate)
    const data = buffer.getChannelData(0)
    
    for (let i = 0; i < bufferSize; i++) {
      data[i] = (Math.random() * 2 - 1) * 0.1
    }
    
    const source = context.createBufferSource()
    source.buffer = buffer
    source.loop = true
    
    const filter = context.createBiquadFilter()
    filter.type = 'lowpass'
    filter.frequency.value = 200 + Math.sin(Date.now() * 0.001) * 50
    
    const ambientGain = context.createGain()
    ambientGain.gain.value = 0.05
    
    source.connect(filter)
    filter.connect(ambientGain)
    ambientGain.connect(gainNodeRef.current)
    
    source.start()
    
    setTimeout(() => {
      source.stop()
    }, 5000)
  }

  // Create magical chime sounds for section transitions
  const createChimeSound = (frequency = 440) => {
    if (!audioContextRef.current || isMuted) return

    const context = audioContextRef.current
    const oscillator = context.createOscillator()
    const envelope = context.createGain()
    
    oscillator.type = 'sine'
    oscillator.frequency.setValueAtTime(frequency, context.currentTime)
    oscillator.frequency.exponentialRampToValueAtTime(frequency * 2, context.currentTime + 0.1)
    oscillator.frequency.exponentialRampToValueAtTime(frequency * 0.5, context.currentTime + 0.5)
    
    envelope.gain.setValueAtTime(0, context.currentTime)
    envelope.gain.linearRampToValueAtTime(0.2, context.currentTime + 0.01)
    envelope.gain.exponentialRampToValueAtTime(0.001, context.currentTime + 1)
    
    oscillator.connect(envelope)
    envelope.connect(gainNodeRef.current)
    
    oscillator.start(context.currentTime)
    oscillator.stop(context.currentTime + 1)
  }

  // Play section-specific sounds
  useEffect(() => {
    if (!isLoaded) return

    const frequencies = [440, 523.25, 659.25, 783.99] // C, C#, E, G#
    createChimeSound(frequencies[currentSection])
  }, [currentSection, isLoaded])

  // Start ambient sounds when loaded
  useEffect(() => {
    if (isLoaded && !isPlaying) {
      setIsPlaying(true)
      const interval = setInterval(createAmbientSound, 6000)
      createAmbientSound() // Start immediately
      
      return () => clearInterval(interval)
    }
  }, [isLoaded, isPlaying, isMuted])

  const toggleMute = () => {
    setIsMuted(!isMuted)
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = isMuted ? 0.1 : 0
    }
  }

  return (
    <div className="audio-controls">
      <button 
        className={`audio-toggle ${isMuted ? 'muted' : ''}`}
        onClick={toggleMute}
        title={isMuted ? 'Unmute' : 'Mute'}
      >
        {isMuted ? '🔇' : '🔊'}
      </button>
    </div>
  )
}

export default AudioManager
