import React, { useState, useEffect } from 'react'

const IntroSequence = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  const introSteps = [
    {
      text: "IGLOO INC",
      subtitle: "Entering the winter wonderland...",
      duration: 2000
    },
    {
      text: "PUDGY PENGUINS",
      subtitle: "Home of the beloved NFT collection",
      duration: 2000
    },
    {
      text: "ONCHAIN REVOLUTION",
      subtitle: "Building the future of digital communities",
      duration: 2000
    }
  ]

  useEffect(() => {
    if (currentStep < introSteps.length) {
      const timer = setTimeout(() => {
        setCurrentStep(currentStep + 1)
      }, introSteps[currentStep].duration)

      return () => clearTimeout(timer)
    } else {
      // Fade out and complete
      const fadeTimer = setTimeout(() => {
        setIsVisible(false)
        setTimeout(onComplete, 500)
      }, 1000)

      return () => clearTimeout(fadeTimer)
    }
  }, [currentStep, onComplete])

  if (!isVisible) return null

  const currentStepData = introSteps[currentStep] || introSteps[introSteps.length - 1]

  return (
    <div className={`intro-sequence ${!isVisible ? 'fade-out' : ''}`}>
      <div className="intro-content">
        <div className="intro-logo">
          <div className="logo-particles">
            {Array.from({ length: 12 }, (_, i) => (
              <div
                key={i}
                className="logo-particle"
                style={{
                  '--delay': `${i * 0.1}s`,
                  '--angle': `${(i / 12) * 360}deg`
                }}
              />
            ))}
          </div>
          <div className="logo-center">❄️</div>
        </div>
        
        <h1 className="intro-title">
          {currentStepData.text}
        </h1>
        
        <p className="intro-subtitle">
          {currentStepData.subtitle}
        </p>
        
        <div className="intro-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${((currentStep + 1) / introSteps.length) * 100}%` }}
            />
          </div>
          <span className="progress-text">
            {currentStep + 1} / {introSteps.length}
          </span>
        </div>
      </div>
      
      {/* Background animation */}
      <div className="intro-background">
        {Array.from({ length: 50 }, (_, i) => (
          <div
            key={i}
            className="bg-particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${5 + Math.random() * 5}s`
            }}
          />
        ))}
      </div>
    </div>
  )
}

export default IntroSequence
