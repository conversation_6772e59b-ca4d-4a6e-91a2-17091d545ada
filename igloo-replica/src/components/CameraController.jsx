import React, { useRef } from 'react'
import { useFrame, useThree } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'

const CameraController = ({ currentSection = 0 }) => {
  const controlsRef = useRef()
  const { camera } = useThree()

  // Define camera positions for each section
  const cameraPositions = [
    { position: [0, 5, 10], target: [0, 0, 0] },     // Igloo Inc - main view
    { position: [-8, 3, 8], target: [-8, 2, -5] },   // Pudgy Penguins - focus on first ice block
    { position: [8, 4, 12], target: [8, 3, -3] },    // Community - focus on second ice block
  ]

  useFrame((state) => {
    if (controlsRef.current) {
      const targetPos = cameraPositions[currentSection]
      
      // Smooth camera transition
      camera.position.lerp(
        { x: targetPos.position[0], y: targetPos.position[1], z: targetPos.position[2] },
        0.02
      )
      
      controlsRef.current.target.lerp(
        { x: targetPos.target[0], y: targetPos.target[1], z: targetPos.target[2] },
        0.02
      )
      
      controlsRef.current.update()
    }
  })

  return (
    <OrbitControls
      ref={controlsRef}
      enablePan={false}
      enableZoom={true}
      enableRotate={true}
      minDistance={5}
      maxDistance={50}
      minPolarAngle={Math.PI / 6}
      maxPolarAngle={Math.PI / 2}
      autoRotate={false}
      autoRotateSpeed={0.5}
      dampingFactor={0.05}
      enableDamping={true}
    />
  )
}

export default CameraController
