import React, { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import { Float, Text } from '@react-three/drei'
import * as THREE from 'three'
import IceMaterial from './IceMaterial'

const IceBlock = ({ position, scale, project, index, isActive = false }) => {
  const meshRef = useRef()
  const groupRef = useRef()

  // Create procedural ice crystal geometry
  const geometry = useMemo(() => {
    const geo = new THREE.BoxGeometry(1, 1, 1, 4, 4, 4)
    const positions = geo.attributes.position.array

    // Distort the cube to create ice crystal effect
    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i]
      const y = positions[i + 1]
      const z = positions[i + 2]

      // Add random distortion
      positions[i] += (Math.random() - 0.5) * 0.2
      positions[i + 1] += (Math.random() - 0.5) * 0.2
      positions[i + 2] += (Math.random() - 0.5) * 0.2

      // Create crystal-like protrusions
      const distance = Math.sqrt(x * x + y * y + z * z)
      const factor = 1 + Math.sin(distance * 5) * 0.1
      positions[i] *= factor
      positions[i + 1] *= factor
      positions[i + 2] *= factor
    }

    geo.computeVertexNormals()
    return geo
  }, [])



  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1
      meshRef.current.rotation.x = Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05
    }

    if (groupRef.current) {
      groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.2
    }
  })

  return (
    <Float
      speed={1 + index * 0.2}
      rotationIntensity={0.2}
      floatIntensity={0.3}
      floatingRange={[-0.5, 0.5]}
    >
      <group ref={groupRef} position={position}>
        <mesh
          ref={meshRef}
          geometry={geometry}
          scale={scale}
          castShadow
          receiveShadow
        >
          <IceMaterial color={isActive ? "#00d4ff" : "#e6f3ff"} />
        </mesh>

        {/* Project label */}
        <Text
          position={[0, scale[1] + 1, 0]}
          fontSize={0.5}
          color="#00d4ff"
          anchorX="center"
          anchorY="middle"
        >
          {project.name}
        </Text>

        {/* Inner glow effect */}
        <pointLight
          position={[0, 0, 0]}
          intensity={isActive ? 0.8 : 0.3}
          color="#00d4ff"
          distance={isActive ? 8 : 5}
          decay={2}
        />
      </group>
    </Float>
  )
}

const IceBlocks = ({ currentSection = 0 }) => {
  const projects = [
    { name: "PUDGY PENGUINS", description: "The original NFT collection" },
    { name: "PUDGY WORLD", description: "Virtual world experience" },
    { name: "PUDGY TOYS", description: "Physical collectibles" },
    { name: "COMMUNITY", description: "Global penguin family" },
  ]

  const positions = [
    [-8, 2, -5],
    [8, 3, -3],
    [-6, 1, 8],
    [10, 2, 6],
  ]

  const scales = [
    [2, 3, 2],
    [2.5, 2, 2.5],
    [1.8, 3.5, 1.8],
    [2.2, 2.8, 2.2],
  ]

  return (
    <group>
      {projects.map((project, index) => (
        <IceBlock
          key={index}
          position={positions[index]}
          scale={scales[index]}
          project={project}
          index={index}
          isActive={currentSection === index + 1}
        />
      ))}
    </group>
  )
}

export default IceBlocks
