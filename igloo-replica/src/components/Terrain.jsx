import React, { useMemo, useRef } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

const Terrain = () => {
  const meshRef = useRef()
  
  // Create a snowy terrain with procedural height variation
  const geometry = useMemo(() => {
    const geo = new THREE.PlaneGeometry(100, 100, 128, 128)
    const positions = geo.attributes.position.array
    
    // Add height variation to create hills and valleys
    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i]
      const y = positions[i + 1]
      
      // Create rolling hills with noise
      const height = 
        Math.sin(x * 0.02) * 2 +
        Math.cos(y * 0.02) * 2 +
        Math.sin(x * 0.05) * 0.5 +
        Math.cos(y * 0.05) * 0.5 +
        Math.random() * 0.2
      
      positions[i + 2] = height
    }
    
    geo.computeVertexNormals()
    return geo
  }, [])

  const material = useMemo(() => {
    return new THREE.MeshStandardMaterial({
      color: '#f0f8ff',
      roughness: 0.8,
      metalness: 0.1,
      transparent: true,
      opacity: 0.9,
    })
  }, [])

  useFrame((state) => {
    if (meshRef.current) {
      // Subtle animation to make the snow feel alive
      meshRef.current.material.opacity = 0.9 + Math.sin(state.clock.elapsedTime * 0.5) * 0.05
    }
  })

  return (
    <>
      {/* Main terrain */}
      <mesh 
        ref={meshRef}
        geometry={geometry}
        material={material}
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, -2, 0]}
        receiveShadow
      />
      
      {/* Additional snow patches */}
      {Array.from({ length: 20 }, (_, i) => (
        <mesh
          key={i}
          position={[
            (Math.random() - 0.5) * 80,
            -1.5 + Math.random() * 0.5,
            (Math.random() - 0.5) * 80
          ]}
          rotation={[-Math.PI / 2, 0, Math.random() * Math.PI * 2]}
        >
          <circleGeometry args={[2 + Math.random() * 3, 16]} />
          <meshStandardMaterial 
            color="#ffffff"
            roughness={0.9}
            metalness={0.0}
            transparent
            opacity={0.7}
          />
        </mesh>
      ))}
    </>
  )
}

export default Terrain
