import React, { useState, useEffect } from 'react'

const LoadingScreen = () => {
  const [loadingText, setLoadingText] = useState('Loading Igloo Inc...')
  const [progress, setProgress] = useState(0)

  const loadingMessages = [
    'Loading Igloo Inc...',
    'Generating ice crystals...',
    'Initializing particle systems...',
    'Preparing winter wonderland...',
    'Almost ready...'
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 20
        if (newProgress >= 100) {
          clearInterval(interval)
          return 100
        }

        const messageIndex = Math.floor((newProgress / 100) * loadingMessages.length)
        setLoadingText(loadingMessages[Math.min(messageIndex, loadingMessages.length - 1)])

        return newProgress
      })
    }, 200)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="loading-screen">
      <div className="loading-content">
        <div className="loading-spinner"></div>
        <h2 className="loading-title">{loadingText}</h2>
        <div className="loading-bar">
          <div
            className="loading-progress"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <p className="loading-percentage">{Math.round(progress)}%</p>
      </div>

      {/* Animated background particles */}
      <div className="loading-particles">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="loading-particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
    </div>
  )
}

export default LoadingScreen
