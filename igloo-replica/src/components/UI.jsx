import React, { useState, useEffect } from 'react'

const UI = () => {
  const [currentSection, setCurrentSection] = useState(0)
  const [isGlitching, setIsGlitching] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  const sections = [
    {
      title: "IGLOO INC",
      subtitle: "Our mission is to create the largest onchain community, driving the consumer crypto revolution."
    },
    {
      title: "PUDGY PENGUINS",
      subtitle: "The beloved NFT collection that started it all. Join thousands of penguin enthusiasts worldwide."
    },
    {
      title: "COMMUNITY",
      subtitle: "Building the future of digital ownership and community-driven experiences in Web3."
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setIsGlitching(true)
      setTimeout(() => setIsGlitching(false), 200)
    }, 8000)

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1
      })
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  useEffect(() => {
    const autoAdvance = setInterval(() => {
      setCurrentSection((prev) => (prev + 1) % sections.length)
      setIsGlitching(true)
      setTimeout(() => setIsGlitching(false), 200)
    }, 6000)

    return () => clearInterval(autoAdvance)
  }, [sections.length])

  const handleSectionChange = (index) => {
    setCurrentSection(index)
    setIsGlitching(true)
    setTimeout(() => setIsGlitching(false), 200)
  }

  return (
    <div className="ui-overlay">
      <div
        className="ui-content"
        style={{
          transform: `translate(${mousePosition.x * 10}px, ${mousePosition.y * 5}px)`
        }}
      >
        <h1
          className={`main-title ${isGlitching ? 'glitch' : ''}`}
          data-text={sections[currentSection].title}
        >
          {sections[currentSection].title}
        </h1>
        <p className="subtitle">
          {sections[currentSection].subtitle}
        </p>

        {/* Social Links */}
        <div className="social-links">
          <a href="#" className="social-link">Twitter</a>
          <a href="#" className="social-link">Discord</a>
          <a href="#" className="social-link">OpenSea</a>
        </div>
      </div>

      <div className="navigation">
        {sections.map((_, index) => (
          <div
            key={index}
            className={`nav-dot ${currentSection === index ? 'active' : ''}`}
            onClick={() => handleSectionChange(index)}
          />
        ))}
      </div>

      {/* Floating cursor effect */}
      <div
        className="cursor-glow"
        style={{
          left: `${(mousePosition.x + 1) * 50}%`,
          top: `${(-mousePosition.y + 1) * 50}%`
        }}
      />
    </div>
  )
}

export default UI
