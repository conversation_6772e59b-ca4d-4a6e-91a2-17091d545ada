import React from 'react'
import { <PERSON><PERSON><PERSON>pose<PERSON>, <PERSON>, ChromaticAberration, Vignette } from '@react-three/postprocessing'
import { BlendFunction } from 'postprocessing'

const PostProcessing = ({ currentSection }) => {
  return (
    <EffectComposer>
      {/* Bloom effect for magical glow */}
      <Bloom
        intensity={0.4}
        luminanceThreshold={0.1}
        luminanceSmoothing={0.9}
        height={300}
      />

      {/* Chromatic aberration for ice crystal effect */}
      <ChromaticAberration
        blendFunction={BlendFunction.NORMAL}
        offset={[0.001, 0.001]}
      />

      {/* Vignette for atmospheric depth */}
      <Vignette
        blendFunction={BlendFunction.NORMAL}
        eskil={false}
        offset={0.1}
        darkness={0.3}
      />
    </EffectComposer>
  )
}

export default PostProcessing
