import React, { useRef, useMemo } from 'react'
import { use<PERSON>rame } from '@react-three/fiber'
import { Float, Center } from '@react-three/drei'
import * as THREE from 'three'
import IceBlocks from './IceBlocks'
import Terrain from './Terrain'
import ParticleSystem from './ParticleSystem'
import Lighting from './Lighting'
import CustomEnvironment from './Environment'
import PostProcessing from './PostProcessing'

const Scene = ({ currentSection = 0 }) => {
  const groupRef = useRef()

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.05
    }
  })

  return (
    <group ref={groupRef}>
      <CustomEnvironment />
      <Lighting />
      <Terrain />
      <IceBlocks currentSection={currentSection} />
      <ParticleSystem />

      {/* Floating 3D Text */}
      <Float
        speed={2}
        rotationIntensity={0.5}
        floatIntensity={0.5}
        floatingRange={[0, 0.5]}
      >
        <Center position={[0, 8, 0]}>
          <mesh>
            <boxGeometry args={[6, 1, 1]} />
            <meshStandardMaterial
              color="#00d4ff"
              emissive="#001122"
              emissiveIntensity={0.2}
              roughness={0.1}
              metalness={0.8}
            />
          </mesh>
        </Center>
      </Float>

      {/* Ambient particles */}
      <group>
        {Array.from({ length: 50 }, (_, i) => (
          <Float
            key={i}
            speed={1 + Math.random()}
            rotationIntensity={0.2}
            floatIntensity={0.3}
            floatingRange={[-2, 2]}
          >
            <mesh
              position={[
                (Math.random() - 0.5) * 40,
                Math.random() * 20,
                (Math.random() - 0.5) * 40
              ]}
            >
              <sphereGeometry args={[0.05, 8, 8]} />
              <meshBasicMaterial
                color="#ffffff"
                transparent
                opacity={0.6}
              />
            </mesh>
          </Float>
        ))}
      </group>

      {/* Post-processing effects */}
      <PostProcessing currentSection={currentSection} />
    </group>
  )
}

export default Scene
