import React, { useMemo, useRef } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

const CustomEnvironment = () => {
  const skyRef = useRef()
  const starsRef = useRef()

  // Create starfield
  const starGeometry = useMemo(() => {
    const geometry = new THREE.BufferGeometry()
    const starCount = 2000
    const positions = new Float32Array(starCount * 3)

    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3
      const radius = 200
      const theta = Math.random() * Math.PI * 2
      const phi = Math.acos(Math.random() * 2 - 1)

      positions[i3] = radius * Math.sin(phi) * Math.cos(theta)
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions[i3 + 2] = radius * Math.cos(phi)
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    return geometry
  }, [])

  const starMaterial = useMemo(() => {
    return new THREE.PointsMaterial({
      color: '#ffffff',
      size: 0.5,
      transparent: true,
      opacity: 0.8,
      sizeAttenuation: false,
    })
  }, [])

  // Create aurora-like background
  const skyGeometry = useMemo(() => {
    return new THREE.SphereGeometry(150, 32, 32)
  }, [])

  const skyMaterial = useMemo(() => {
    const vertexShader = `
      varying vec3 vWorldPosition;
      void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform float time;
      varying vec3 vWorldPosition;

      vec3 aurora(vec3 pos, float time) {
        float y = pos.y / 150.0;
        float wave1 = sin(pos.x * 0.01 + time * 0.5) * 0.5 + 0.5;
        float wave2 = sin(pos.z * 0.01 + time * 0.3) * 0.5 + 0.5;
        
        vec3 color1 = vec3(0.0, 0.2, 0.4); // Deep blue
        vec3 color2 = vec3(0.0, 0.6, 0.8); // Cyan
        vec3 color3 = vec3(0.2, 0.8, 0.6); // Green-cyan
        
        float factor = smoothstep(-0.5, 1.0, y);
        vec3 gradient = mix(color1, color2, factor);
        gradient = mix(gradient, color3, wave1 * wave2 * factor);
        
        return gradient;
      }

      void main() {
        vec3 color = aurora(vWorldPosition, time);
        gl_FragColor = vec4(color, 1.0);
      }
    `

    return new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        time: { value: 0 }
      },
      side: THREE.BackSide,
    })
  }, [])

  useFrame((state) => {
    if (skyRef.current) {
      skyRef.current.material.uniforms.time.value = state.clock.elapsedTime
    }
    if (starsRef.current) {
      starsRef.current.rotation.y = state.clock.elapsedTime * 0.01
    }
  })

  return (
    <group>
      {/* Aurora sky */}
      <mesh ref={skyRef} geometry={skyGeometry} material={skyMaterial} />
      
      {/* Stars */}
      <points ref={starsRef} geometry={starGeometry} material={starMaterial} />
      
      {/* Atmospheric fog */}
      <fog attach="fog" args={['#0a1a2e', 50, 200]} />
    </group>
  )
}

export default CustomEnvironment
