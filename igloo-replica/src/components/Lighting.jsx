import React, { useRef } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

const Lighting = () => {
  const directionalLightRef = useRef()
  const pointLightRef = useRef()

  useFrame((state) => {
    if (directionalLightRef.current) {
      directionalLightRef.current.position.x = Math.sin(state.clock.elapsedTime * 0.5) * 10
      directionalLightRef.current.position.z = Math.cos(state.clock.elapsedTime * 0.5) * 10
    }
    
    if (pointLightRef.current) {
      pointLightRef.current.intensity = 0.5 + Math.sin(state.clock.elapsedTime * 2) * 0.2
    }
  })

  return (
    <>
      {/* Ambient light for overall illumination */}
      <ambientLight intensity={0.3} color="#4a90e2" />
      
      {/* Main directional light (sun/moon) */}
      <directionalLight
        ref={directionalLightRef}
        position={[10, 10, 5]}
        intensity={1}
        color="#ffffff"
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-20}
        shadow-camera-right={20}
        shadow-camera-top={20}
        shadow-camera-bottom={-20}
      />
      
      {/* Accent point light */}
      <pointLight
        ref={pointLightRef}
        position={[0, 5, 0]}
        intensity={0.5}
        color="#00d4ff"
        distance={20}
        decay={2}
      />
      
      {/* Rim lighting */}
      <directionalLight
        position={[-10, 5, -10]}
        intensity={0.3}
        color="#ff6b6b"
      />
      
      {/* Fill light */}
      <hemisphereLight
        skyColor="#87ceeb"
        groundColor="#1e3a8a"
        intensity={0.4}
      />
    </>
  )
}

export default Lighting
