import React, { Suspense, useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { Environment } from '@react-three/drei'
import Scene from './components/Scene'
import UI from './components/UI'
import LoadingScreen from './components/LoadingScreen'
import CameraController from './components/CameraController'
import AudioManager from './components/AudioManager'
import IntroSequence from './components/IntroSequence'
import './App.css'

function App() {
  const [currentSection, setCurrentSection] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)
  const [showIntro, setShowIntro] = useState(true)

  return (
    <div className="app">
      {showIntro && (
        <IntroSequence onComplete={() => setShowIntro(false)} />
      )}

      <Suspense fallback={<LoadingScreen />}>
        <Canvas
          camera={{ position: [0, 5, 10], fov: 75 }}
          gl={{ antialias: true, alpha: false }}
          dpr={[1, 2]}
          onCreated={() => setTimeout(() => setIsLoaded(true), 2000)}
        >
          <Environment preset="winter" />
          <CameraController currentSection={currentSection} />
          <Scene currentSection={currentSection} />
        </Canvas>
        <UI currentSection={currentSection} setCurrentSection={setCurrentSection} />
        <AudioManager currentSection={currentSection} isLoaded={isLoaded} />
      </Suspense>
    </div>
  )
}

export default App
